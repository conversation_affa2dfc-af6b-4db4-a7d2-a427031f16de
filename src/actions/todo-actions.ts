"use server";

import { createClient } from "@/integrations/supabase/client";
import { revalidatePath } from "next/cache";

export interface Todo {
  id: string;
  text: string;
  completed: boolean;
  created_at: string;
  completed_at?: string;
  date_key: string;
  user_id?: string;
  updated_at?: string;
}

// 获取用户的todos
export async function getTodos(): Promise<Todo[]> {
  try {
    const supabase = createClient(
      "https://fdrbawfbwqznypxeiexx.supabase.co",
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
    );
    
    const { data, error } = await supabase
      .from("todos")
      .select("*")
      .order("created_at", { ascending: false });
    
    if (error) {
      console.error("Error fetching todos:", error);
      throw new Error(`Failed to fetch todos: ${error.message}`);
    }
    
    // Ensure all dates are properly serialized
    return data.map(todo => ({
      ...todo,
      created_at: new Date(todo.created_at).toISOString(),
      completed_at: todo.completed_at ? new Date(todo.completed_at).toISOString() : undefined,
    }));
  } catch (error) {
    console.error("Error in getTodos:", error);
    throw error;
  }
}

// 添加新的todo
export async function addTodo(text: string, dateKey: string): Promise<Todo> {
  try {
    const supabase = createClient(
      "https://fdrbawfbwqznypxeiexx.supabase.co",
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
    );
    
    const { data, error } = await supabase
      .from("todos")
      .insert({
        text,
        date_key: dateKey,
      })
      .select()
      .single();
    
    if (error) {
      console.error("Error adding todo:", error);
      throw new Error(`Failed to add todo: ${error.message}`);
    }
    
    revalidatePath("/");
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Error in addTodo:", error);
    throw error;
  }
}

// 切换todo的完成状态
export async function toggleTodo(id: string): Promise<Todo> {
  try {
    const supabase = createClient(
      "https://fdrbawfbwqznypxeiexx.supabase.co",
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
    );
    
    // 先获取当前todo的状态
    const { data: currentTodo, error: fetchError } = await supabase
      .from("todos")
      .select("completed, completed_at")
      .eq("id", id)
      .single();
    
    if (fetchError) {
      console.error("Error fetching todo:", fetchError);
      throw new Error(`Failed to fetch todo: ${fetchError.message}`);
    }
    
    const { data, error } = await supabase
      .from("todos")
      .update({
        completed: !currentTodo.completed,
        completed_at: !currentTodo.completed ? new Date().toISOString() : null,
      })
      .eq("id", id)
      .select()
      .single();
    
    if (error) {
      console.error("Error toggling todo:", error);
      throw new Error(`Failed to toggle todo: ${error.message}`);
    }
    
    revalidatePath("/");
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Error in toggleTodo:", error);
    throw error;
  }
}

// 删除todo
export async function deleteTodo(id: string): Promise<boolean> {
  try {
    const supabase = createClient(
      "https://fdrbawfbwqznypxeiexx.supabase.co",
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
    );
    
    const { error } = await supabase
      .from("todos")
      .delete()
      .eq("id", id);
    
    if (error) {
      console.error("Error deleting todo:", error);
      throw new Error(`Failed to delete todo: ${error.message}`);
    }
    
    revalidatePath("/");
    return true;
  } catch (error) {
    console.error("Error in deleteTodo:", error);
    throw error;
  }
}

// 编辑todo文本
export async function editTodo(id: string, newText: string): Promise<Todo> {
  try {
    const supabase = createClient(
      "https://fdrbawfbwqznypxeiexx.supabase.co",
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
    );
    
    const { data, error } = await supabase
      .from("todos")
      .update({
        text: newText,
      })
      .eq("id", id)
      .select()
      .single();
    
    if (error) {
      console.error("Error editing todo:", error);
      throw new Error(`Failed to edit todo: ${error.message}`);
    }
    
    revalidatePath("/");
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Error in editTodo:", error);
    throw error;
  }
}