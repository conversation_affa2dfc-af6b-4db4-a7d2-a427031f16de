import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/integrations/supabase/client';

const supabase = createClient(
  "https://fdrbawfbwqznypxeiexx.supabase.co",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const { data, error } = await supabase
      .from("todos")
      .select("*")
      .eq("id", id)
      .single();
    
    if (error) {
      console.error("Error fetching todo:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in GET /api/todos/[id]:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { text } = await request.json();
    
    if (!text) {
      return NextResponse.json({ error: "Missing text field" }, { status: 400 });
    }
    
    const { data, error } = await supabase
      .from("todos")
      .update({
        text,
      })
      .eq("id", id)
      .select()
      .single();
    
    if (error) {
      console.error("Error updating todo:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in PUT /api/todos/[id]:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const { error } = await supabase
      .from("todos")
      .delete()
      .eq("id", id);
    
    if (error) {
      console.error("Error deleting todo:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error in DELETE /api/todos/[id]:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // 先获取当前todo的状态
    const { data: currentTodo, error: fetchError } = await supabase
      .from("todos")
      .select("completed, completed_at")
      .eq("id", id)
      .single();
    
    if (fetchError) {
      console.error("Error fetching todo:", fetchError);
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }
    
    const { data, error } = await supabase
      .from("todos")
      .update({
        completed: !currentTodo.completed,
        completed_at: !currentTodo.completed ? new Date().toISOString() : null,
      })
      .eq("id", id)
      .select()
      .single();
    
    if (error) {
      console.error("Error toggling todo:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in PATCH /api/todos/[id]/toggle:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}