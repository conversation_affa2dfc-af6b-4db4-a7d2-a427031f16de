import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/integrations/supabase/client';

const supabase = createClient(
  "https://fdrbawfbwqznypxeiexx.supabase.co",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY"
);

export async function GET() {
  try {
    const { data, error } = await supabase
      .from("todos")
      .select("*")
      .order("created_at", { ascending: false });
    
    if (error) {
      console.error("Error fetching todos:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in GET /api/todos:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { text, dateKey } = await request.json();
    
    if (!text || !dateKey) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    
    const { data, error } = await supabase
      .from("todos")
      .insert({
        text,
        date_key: dateKey,
      })
      .select()
      .single();
    
    if (error) {
      console.error("Error adding todo:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in POST /api/todos:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}