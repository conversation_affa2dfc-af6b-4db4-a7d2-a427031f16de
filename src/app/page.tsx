"use client";

import { useState, useEffect } from "react";
import { TodoInput } from "@/components/todo-input";
import { TodoList } from "@/components/todo-list";
import { TodoStats } from "@/components/todo-stats";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { ClientOnly } from "@/components/client-only";
import { Todo } from "@/types/todo";
import { getTodayDateKey } from "@/utils/date-utils";

// 直接在客户端组件中定义数据获取函数
async function getTodos(): Promise<Todo[]> {
  try {
    const response = await fetch('/api/todos', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data.map((todo: any) => ({
      ...todo,
      created_at: new Date(todo.created_at).toISOString(),
      completed_at: todo.completed_at ? new Date(todo.completed_at).toISOString() : undefined,
    }));
  } catch (error) {
    console.error("Failed to fetch todos:", error);
    return [];
  }
}

async function addTodo(text: string, dateKey: string): Promise<Todo | null> {
  try {
    const response = await fetch('/api/todos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, dateKey }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Failed to add todo:", error);
    return null;
  }
}

async function toggleTodo(id: string): Promise<Todo | null> {
  try {
    const response = await fetch(`/api/todos/${id}/toggle`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Failed to toggle todo:", error);
    return null;
  }
}

async function deleteTodo(id: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/todos/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return true;
  } catch (error) {
    console.error("Failed to delete todo:", error);
    return false;
  }
}

async function editTodo(id: string, newText: string): Promise<Todo | null> {
  try {
    const response = await fetch(`/api/todos/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: newText }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return {
      ...data,
      created_at: new Date(data.created_at).toISOString(),
      completed_at: data.completed_at ? new Date(data.completed_at).toISOString() : undefined,
    };
  } catch (error) {
    console.error("Failed to edit todo:", error);
    return null;
  }
}

export default function Home() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从API加载数据
  useEffect(() => {
    const fetchTodos = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const fetchedTodos = await getTodos();
        setTodos(fetchedTodos);
      } catch (error) {
        console.error("Failed to fetch todos:", error);
        setError("加载待办事项失败，请稍后重试");
        setTodos([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTodos();
  }, []);

  const addTodoHandler = async (text: string, dateKey: string) => {
    try {
      const newTodo = await addTodo(text, dateKey);
      if (newTodo) {
        setTodos(prevTodos => [newTodo, ...prevTodos]);
      }
    } catch (error) {
      console.error("Failed to add todo:", error);
    }
  };

  const toggleTodoHandler = async (id: string) => {
    try {
      const updatedTodo = await toggleTodo(id);
      if (updatedTodo) {
        setTodos(prevTodos =>
          prevTodos.map(todo =>
            todo.id === id ? updatedTodo : todo
          )
        );
      }
    } catch (error) {
      console.error("Failed to toggle todo:", error);
    }
  };

  const deleteTodoHandler = async (id: string) => {
    try {
      const success = await deleteTodo(id);
      if (success) {
        setTodos(prevTodos => prevTodos.filter(todo => todo.id !== id));
      }
    } catch (error) {
      console.error("Failed to delete todo:", error);
    }
  };

  const editTodoHandler = async (id: string, newText: string) => {
    try {
      const updatedTodo = await editTodo(id, newText);
      if (updatedTodo) {
        setTodos(prevTodos =>
          prevTodos.map(todo =>
            todo.id === id ? updatedTodo : todo
          )
        );
      }
    } catch (error) {
      console.error("Failed to edit todo:", error);
    }
  };

  const completedCount = todos.filter((todo) => todo.completed).length;
  const totalCount = todos.length;

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-8">
          <div className="text-destructive text-lg font-medium">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <ClientOnly fallback={
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载中...</p>
        </div>
      </div>
    }>
      <div className="max-w-2xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Todo 清单</h1>
          <p className="text-muted-foreground">按日期管理你的日常任务</p>
        </div>

        <div className="bg-card border rounded-lg p-6 space-y-6">
          <TodoInput onAdd={addTodoHandler} />

          <TodoStats total={totalCount} completed={completedCount} />

          <TodoList
            todos={todos}
            onToggle={toggleTodoHandler}
            onDelete={deleteTodoHandler}
            onEdit={editTodoHandler}
          />
        </div>

        <MadeWithDyad />
      </div>
    </ClientOnly>
  );
}