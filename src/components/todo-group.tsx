"use client";

import { TodoItem } from "./todo-item";
import { TodoGroup as TodoGroupType, Todo } from "@/types/todo";
import { getDateDisplay } from "@/utils/date-utils";

interface TodoGroupProps {
  group: TodoGroupType;
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
  onEdit: (id: string, newText: string) => void;
}

export function TodoGroup({ group, onToggle, onDelete, onEdit }: TodoGroupProps) {
  const completionRate = group.totalCount > 0 
    ? Math.round((group.completedCount / group.totalCount) * 100) 
    : 0;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          {getDateDisplay(group.dateKey)}
        </h3>
        <span className="text-sm text-muted-foreground">
          {group.completedCount}/{group.totalCount} ({completionRate}%)
        </span>
      </div>
      
      <div className="space-y-2">
        {group.todos.map((todo: Todo) => (
          <TodoItem
            key={todo.id}
            id={todo.id}
            text={todo.text}
            completed={todo.completed}
            created_at={todo.created_at}
            onToggle={onToggle}
            onDelete={onDelete}
            onEdit={onEdit}
          />
        ))}
      </div>
    </div>
  );
}