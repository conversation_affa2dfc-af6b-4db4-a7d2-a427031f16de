"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus } from "lucide-react";
import { getTodayDateKey } from "@/utils/date-utils";

interface TodoInputProps {
  onAdd: (text: string, dateKey: string) => void;
}

export function TodoInput({ onAdd }: TodoInputProps) {
  const [inputValue, setInputValue] = useState("");
  const [selectedDate, setSelectedDate] = useState(getTodayDateKey());

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onAdd(inputValue.trim(), selectedDate);
      setInputValue("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex gap-2">
        <Input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="添加新的待办事项..."
          className="flex-1"
          onKeyDown={(e) => {
            if (e.key === "Enter") handleSubmit(e);
          }}
        />
        <Button type="submit" size="icon" disabled={!inputValue.trim()}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="flex items-center gap-2">
        <label htmlFor="todo-date" className="text-sm text-muted-foreground">
          日期：
        </label>
        <input
          type="date"
          id="todo-date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="px-3 py-1 border rounded-md text-sm"
        />
      </div>
    </form>
  );
}