"use client";

import { TodoGroup } from "./todo-group";
import { TodoGroup as TodoGroupType } from "@/types/todo";
import { groupTodosByDate } from "@/utils/date-utils";

interface Todo {
  id: string;
  text: string;
  completed: boolean;
  created_at: string;
  completed_at?: string;
  date_key: string;
}

interface TodoListProps {
  todos: Todo[];
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
  onEdit: (id: string, newText: string) => void;
}

export function TodoList({ todos, onToggle, onDelete, onEdit }: TodoListProps) {
  const todoGroups = groupTodosByDate(todos);

  if (todoGroups.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        暂无待办事项，添加一个吧！
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {todoGroups.map((group: TodoGroupType) => (
        <TodoGroup
          key={group.dateKey}
          group={group}
          onToggle={onToggle}
          onDelete={onDelete}
          onEdit={onEdit}
        />
      ))}
    </div>
  );
}