"use client";

import { useEffect, useState } from "react";

interface TodoStatsProps {
  total: number;
  completed: number;
}

export function TodoStats({ total, completed }: TodoStatsProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 在服务器端和首次客户端渲染时显示0，避免水合不匹配
  const displayTotal = isClient ? total : 0;
  const displayCompleted = isClient ? completed : 0;
  const completionRate = displayTotal > 0 ? Math.round((displayCompleted / displayTotal) * 100) : 0;

  return (
    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
      <span>总计: {displayTotal} 项</span>
      <span>已完成: {displayCompleted} 项</span>
      <span>完成率: {completionRate}%</span>
    </div>
  );
}