// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://fdrbawfbwqznypxeiexx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkcmJhd2Zid3F6bnlweGVpZXh4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODk5ODEsImV4cCI6MjA3MDA2NTk4MX0.wxRWofQ8i_HqcyPKWiUl3qG8yz1CD_d4w3EkGIRFAeY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Export createClient function
export { createClient } from '@supabase/supabase-js';