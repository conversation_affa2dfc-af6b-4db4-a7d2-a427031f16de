import { Todo, TodoGroup } from "@/types/todo";

export function formatDateKey(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function getDateKey(date: Date): string {
  return formatDateKey(date);
}

export function getTodayDateKey(): string {
  return formatDateKey(new Date());
}

export function getDateDisplay(dateKey: string): string {
  const date = new Date(dateKey + 'T00:00:00');
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (dateKey === formatDateKey(today)) {
    return '今天';
  } else if (dateKey === formatDateKey(yesterday)) {
    return '昨天';
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'short'
    });
  }
}

export function groupTodosByDate(todos: Todo[]): TodoGroup[] {
  const groups = new Map<string, TodoGroup>();
  
  todos.forEach(todo => {
    const dateKey = todo.date_key;
    if (!groups.has(dateKey)) {
      groups.set(dateKey, {
        dateKey,
        date: new Date(dateKey + 'T00:00:00'),
        todos: [],
        completedCount: 0,
        totalCount: 0
      });
    }
    
    const group = groups.get(dateKey)!;
    group.todos.push(todo);
    group.totalCount++;
    if (todo.completed) {
      group.completedCount++;
    }
  });
  
  return Array.from(groups.values()).sort((a, b) => 
    new Date(b.dateKey).getTime() - new Date(a.dateKey).getTime()
  );
}