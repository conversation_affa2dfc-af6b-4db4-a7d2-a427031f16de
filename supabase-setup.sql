-- 创建 todos 表（如果不存在）
CREATE TABLE IF NOT EXISTS todos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  text TEXT NOT NULL,
  completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  date_key TEXT NOT NULL,
  user_id UUID,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全策略
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Allow anonymous access" ON todos;
DROP POLICY IF EXISTS "Allow all operations for anonymous users" ON todos;

-- 创建允许匿名用户进行所有操作的策略
CREATE POLICY "Allow all operations for anonymous users" ON todos
FOR ALL
TO anon
USING (true)
WITH CHECK (true);

-- 创建允许认证用户进行所有操作的策略
CREATE POLICY "Allow all operations for authenticated users" ON todos
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- 创建更新 updated_at 字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_todos_updated_at ON todos;
CREATE TRIGGER update_todos_updated_at
    BEFORE UPDATE ON todos
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
